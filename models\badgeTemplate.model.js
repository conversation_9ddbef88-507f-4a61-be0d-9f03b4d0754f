const { MEDIA } = require("../config/attributes");
const history = require("../models/plugins/history.plugin");
const media = require("../models/plugins/media.plugin");

module.exports = (sequelize, DataTypes) => {
  const BadgeTemplate = sequelize.define(
    "BadgeTemplate",
    {
      badge_template_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      status: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
      },
      images: {
        type: MEDIA,
        allowNull: true,
        allowMultiple: true,
      },
      config: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "badge_template",
      timestamps: true,
      underscored: true,
    }
  );

  BadgeTemplate.associate = (models) => {
    // Define associations here if needed
    // e.g., BadgeTemplate.hasMany(models.UserBadge, { foreignKey: 'badge_template_id', as: 'user_badges', onDelete: 'CASCADE' });
  };

  // Attach history and media plugins
  history(BadgeTemplate, sequelize, DataTypes);
  media(BadgeTemplate, sequelize, DataTypes);

  return BadgeTemplate;
};
